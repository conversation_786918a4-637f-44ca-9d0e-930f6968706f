{"@@locale": "es", "appTitle": "Estimat KeyMoments", "selectLanguageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "englishLanguage": "Inglés", "portugueseLanguage": "Portugués", "spanishLanguage": "Español", "onboardingScreenTitle": "Bienvenido a Estimat KeyMoments", "onboardingScene1Message": "El ruido puede simplificarse en notas y convertirse en música", "onboardingScene1MessagePart2": "Con un poco de proporción...", "onboardingScene2Message": "Cualquier imagen puede ser replicada a través de luz, sombra y color, y transformada en arte", "onboardingScene2MessagePart2": "Con un poco de proporción...", "onboardingScene3Message": "Tu sobrecarga cognitiva—¿podrías refinar lo que más valoras en ti mismo?", "onboardingScene3MessagePart2": "Con un poco de ...", "onboardingScene4Message": "Con la teoría de la información, una IA puede reducir datos complejos al 5% y reconstruirlos con 95% de fidelidad funcional; estos son vectores latentes", "onboardingScene4MessagePart2": "Tal vez podrías aplicar un poco de eso a tus valores latentes...", "onboardingFlowchartMessage": "Identifiquemos nuestros momentos de referencia. Rastreemos los flujos asociados con esos momentos. Evaluemos según su función. Descubramos los valores latentes que nos impulsan:", "onboardingFlowchartTitle": "Flujo del Proceso", "nextButton": "Siguient<PERSON>", "previousButton": "Anterior", "getStartedButton": "Comenzar", "exitToAppButton": "Salir de la aplicación", "motivationAppBarTitle": "Momentos de Motivación", "satisfactionAppBarTitle": "Momentos de Satisfacción", "selectMomentTypeTitle": "Seleccionar <PERSON>", "selectImprovementWorsening": "Elige entre Mejora o Empeoramiento:", "selectMotivationSatisfaction": "Elige entre Motivación o Satisfacción:", "improvesLabel": "<PERSON><PERSON><PERSON>", "worsensLabel": "Empeora", "motivationLabel": "Motivación", "satisfactionLabel": "Satisfacción", "elementalLabel": "Elemental", "personalLabel": "Personal", "informationalLabel": "Informacional", "socialLabel": "Social", "elementalExplanation": "En función de los estímulos del entorno, damos una respuesta que fue modulada por la evolución. Más correlacionado con actividades físicas.", "personalExplanation": "Sumándose a la memoria que viene de nuestros ancestros, tenemos una memoria que se crea a partir de la interacción única entre un individuo y su entorno. Cuanto más dolor, más adaptativo para la supervivencia. Cuanto más placenteras, más motivador para la supervivencia. Más correlacionado con hobbies, gustos, autoconocimiento.", "informationalExplanation": "Al llegar al nivel intelectual, además de integrar memorias y experiencias con el entorno, logramos una capacidad única: relacionar recuerdos o memorias propias de un individuo entre sí y codificar la información abstracta (ejemplo: signos matemáticos o de lenguaje).", "socialExplanation": "Las relaciones sociales implican la conexión de información entre individuos, el intercambio de cultura y la comunicación. Podemos definir un límite entre nivel intelectual y nivel social cuando utilizamos nuestros procesos intelectuales para interactuar con otros seres capaces de combinar sus propias memorias.", "inwardLabel": "Interior", "outwardLabel": "Exterior", "inwardExplanation": "Reflexivo: <PERSON> almacenar, transformarte a ti mismo, predecir, empatizar.", "outwardExplanation": "Intuitivo: <PERSON> eje<PERSON>ar, dis<PERSON><PERSON><PERSON>, rast<PERSON>r, colaborar.", "titleInputHint": "Ingresa un título de dos palabras", "descriptionInputHint": "Describe este momento", "evidenceInputHint": "Proporciona evidencia para esta distribución", "describeDistributionHint": "Describe por qué elegiste esta distribución...", "firstMomentEvidenceHint": "Explica por qué este momento crea más posibilidades que simplemente estar vivo. ¿Qué nuevas oportunidades, caminos o potencial abre?", "comparisonEvidenceHint": "Explica cuántas posibilidades crea este momento comparado con tu línea base de vida...", "lifePossibilitiesFactorTitle": "Factor de Posibilidades de Vida", "howManyPossibilitiesTitle": "¿Cuántas Posibilidades Crea Este Momento?", "comparedToLifeBaseline": "Comparado con tu línea base de vida, este momento crea:", "morePossibilitiesButton": "Más Posibilidades", "fewerPossibilitiesButton": "Menos Posibilidades", "vsLifeBaseline": "vs Línea Base de Vida", "explainWhyFirstMomentTitle": "Explica Por Qué Este Momento Crea Más Posibilidades Que La Vida", "provideEvidenceTitle": "Proporciona Evidencia para esta Evaluación de Posibilidades", "continueButton": "<PERSON><PERSON><PERSON><PERSON>", "lifePossibilitiesChart": "Gráfico de Posibilidades de Vida", "allTimeFilter": "Todo el Tiempo", "lastWeekFilter": "Última Semana", "lastMonthFilter": "<PERSON><PERSON><PERSON>", "last3MonthsFilter": "Últimos 3 Meses", "currentPreviewLabel": "Vista Previa Actual:", "lifeLabel": "Vida", "previewLabel": "Vista Previa", "lifeBaselineLabel": "Línea Base de Vida (1x)", "morePossibilitiesLabel": "Más Posibilidades", "fewerPossibilitiesLabel": "Menos Posibilidades", "currentPreviewLegend": "Vista Previa Actual", "lifePossibilitiesExplanation": "Compara este momento con tu línea base de vida (1x). ¿Cuántas veces más posibilidades crea este momento para ti? Puedes ir por encima o por debajo de momentos anteriores, pero nunca por debajo de la línea base de vida.\\n\\nEl cálculo factorial representa el crecimiento exponencial de posibilidades que los momentos significativos pueden crear en tu vida.", "minimumLifeBaselineNote": "Mínimo: 1.0x (línea base de vida)", "guardianLabel": "Guardián", "warriorLabel": "Guerrero", "versatileLabel": "<PERSON><PERSON><PERSON><PERSON>", "funLabel": "Divertido", "strategistLabel": "Estratega", "tacticalLabel": "Táctico", "altruistLabel": "Altruista", "collaboratorLabel": "Colaborador", "summaryTitle": "Resumen", "motivationSectionTitle": "Análisis de Motivación", "satisfactionSectionTitle": "Análisis de Satisfacción", "latentValuesSectionTitle": "Valores Latentes", "improvesMotivationLabel": "Mejora Motivación", "worsensMotivationLabel": "Empeora Motivación", "improvesSatisfactionLabel": "<PERSON><PERSON><PERSON>", "worsensSatisfactionLabel": "Empeora <PERSON>", "proportionLabel": "Proporción", "exportDataButton": "Exportar Datos", "viewOnboardingMenu": "Introducción", "viewFullPresentationMenu": "Presentación Completa", "viewLevelProcessFocusMenu": "Niveles y Direcciones", "viewLatentValuesMenu": "Funciones Evolutivas y Valores", "fromIACodeToHumanValuesMenu": "Del código IA a los valores humanos", "jacoMinestSupportMenu": "Apoyo Jacominest", "changeLanguageMenu": "Cambiar I<PERSON>", "supportScreenTitle": "Apoyo", "supportMainMessage": "<PERSON>var una mente salva más que mil latas.", "supportIntroText": "Las chances pueden no estar mucho a favor, pero salvar una mente que puede estar perdida, desesperanzada o atrapada en ciclos destructivos puede generar retornos exponenciales a largo plazo.", "supportEvidenceTitle": "Evidencias", "supportStatistic1": "Las personas que participan en programas educativos en prisión tienen 43% menos probabilidades de regresar.", "supportStatistic1Source": "RAND Corporation (2013). \"Evaluating the Effectiveness of Correctional Education\"", "supportStatistic1Link": "https://www.rand.org/pubs/research_reports/RR266.html", "supportStatistic2": "Cada dólar invertido en educación penitenciaria puede ahorrar entre cuatro y cinco dólares en costos de re-encarcelamiento.", "supportStatistic2Source": "<PERSON>, <PERSON><PERSON> et al. (2013). \"How Effective Is Correctional Education, and Where Do We Go from Here?\"", "supportStatistic2Link": "https://bja.ojp.gov/sites/g/files/xyckuh186/files/Publications/RAND_Correctional-Education-Meta-Analysis.pdf", "supportStatistic3": "El empleo después del alta es 13% más alto entre quienes participaron en programas educativos...", "supportStatistic3Source": "Bureau of Justice Statistics, U.S. Department of Justice", "supportStatistic3Link": "https://bjs.ojp.gov/topics/corrections", "supportConsumptionTitle": "Personas y Reciclaje", "supportConsumptionSubtitle": "Además que una persona a lo largo de su vida consume:", "supportConsumption1": "Miles de kilos de alimentos y envases", "supportConsumption2": "Toneladas de agua", "supportConsumption3": "Energía eléctrica equivalente a años de consumo doméstico", "supportConsumption4": "Materiales de construcción, ropa, tecnología...", "expandToSeeMore": "Toca para ver más", "tapToCollapse": "Toca para contraer", "supportAppDescription": "Esta app fue creada con años de dedicación para mejorar valores latentes usando conocimiento moderno y matemático.", "supportThanksJacominest": "Gracias asociación Jacominesp por apoyar y formas de valorar mejor los recursos del mundo, empezando por quienes más necesitan esperanza.", "supportThanksWitness": "<PERSON><PERSON><PERSON> por ser testigo de su lucha, <PERSON>.", "supportSourcesTitle": "Fuentes:", "supportSource1": "RAND Corporation (2013). \"Evaluating the Effectiveness of Correctional Education\"", "supportSource2": "<PERSON>, <PERSON><PERSON> et al. (2013). \"How Effective Is Correctional Education, and Where Do We Go from Here?\"", "supportSource3": "Bureau of Justice Statistics, U.S. Department of Justice", "continueToWebsiteButton": "Continuar al Sitio Web", "saveButton": "Guardar", "cancelButton": "<PERSON><PERSON><PERSON>", "backButton": "Atrás", "whyMomentsScreenTitle": "¿Por qué Momentos?", "whyMomentsMenuTitle": "¿Por qué Momentos?", "whyHierarchicalMenuTitle": "¿Por qué Organización Jerárquica?", "whyHierarchicalScreenTitle": "¿Por qué Organización Jerárquica?", "levelsAndDirectionsScreenTitle": "Niveles y Direcciones", "skipForNowButton": "<PERSON><PERSON><PERSON> por ahora", "noMomentsRecordedYet": "Aún no se han registrado momentos.", "pageCounter": "{current} / {total}", "welcomeToEstimatKeyMoments": "Bienvenido a Estimat KeyMoments", "estimatKeyMomentsDescription": "Una metodología integral para comprender y analizar los momentos clave que dan forma a tus decisiones de vida y crecimiento personal.", "editThisMomentButton": "Editar este momento", "closeButton": "<PERSON><PERSON><PERSON>", "insertNewMomentButton": "Insertar un nuevo momento", "viewAllMomentsButton": "Ver Todos los Momentos", "momentsHistoryTitle": "Historial de Momentos", "momentSavedTitle": "{momentType} Guardado", "momentSavedMessage": "Tu momento ha sido guardado exitosamente.", "factorialComparisonLabel": "Comparación Factorial: {comparisonType}", "improvementLabel": "<PERSON><PERSON><PERSON>", "worseningLabel": "Empeoramiento", "factorialSliderValueLabel": "Valor del Deslizador Factorial: {value}", "factorialMultiplierLabel": "Multiplicador Factorial: {value}", "whyRegisterMomentsTitle": "¿Por qué registrar momentos objetivamente?", "whyRegisterMomentsContent": "Tus picos y valles emocionales no son solo datos sueltos: son como las coordenadas de tu brújula interna. Registrar tu motivación (qué tan impulsado te sientes) y tu satisfacción (qué tan pleno te sientes) te da un mapa claro para planificar metas, rutinas o tareas que realmente sumen a tu vida.", "whenNoticeMoreTitle": "¿<PERSON>uándo se nota más esto?", "whenNoticeMoreContent": "Cuando estás en un bajón emocional. En esos momentos, tu cerebro te engaña con pensamientos como:\n• \"Nunca me sale nada bien.\"\n• \"No hay nada que me motive.\"\n\nAunque esto no sea cierto, el sesgo de memoria—esa tendencia a recordar lo negativo o lo más reciente—hace que lo sientas así.\n\n📈 Llevar un registro objetivo de tus momentos altos te da pruebas reales de quién sos cuando estás bien, para que no te pierdas en la niebla cuando estás mal.", "highMotivationSatisfactionTitle": "Alta motivación y alta satisfacción", "highMotivationSatisfactionContent": "Pregunta rápida: ¿Qué fue lo más significativo que te pasó este mes?\n\nSi tuvieras un registro diario de tus emociones, ¿responderías lo mismo? Probablemente no. Y hay una razón:\n\n• Sesgo de memoria: <PERSON><PERSON><PERSON> (1979), nuestro cerebro tiende a darle más peso a lo reciente o lo intenso (esto se llama la regla del pico-final). Por eso, a veces olvidamos momentos valiosos que no fueron tan \"ruidosos\".\n• Solución: Anotar tus picos compensa esta trampa mental y te muestra el panorama completo.", "evolutionaryViewTitle": "Mirada evolutiva", "evolutionaryViewContent": "Las emociones positivas fuertes—like orgullo o plenitud—son señales de oportunidades: una relación que funciona, un logro personal o una decisión que te alinea con lo que querés. Nuestros antepasados sobrevivían mejor si recordaban estos momentos para repetirlos. Tu cerebro no está diseñado solo para hacerte feliz, sino para ayudarte a prosperar. Si sabés qué te motiva, podés buscarlo más seguido.", "practiceEstimatTitle": "Práctica con ESTIMAT:", "practiceEstimatContent": "Imaginá que anotás:\n\n✍️ \"Me sentí poderoso explicando mis emociones en un taller sin que me juzgaran.\"\n\n🔁 Al revisar varias entradas parecidas, ESTIMAT te muestra:\n• Alta motivación cuando expresás lo que sentís.\n• Alta satisfacción con escucha activa y validación.\n\n🧭 ¿Qué hacés con esto?\n• Buscá o creá más situaciones así (talleres, charlas con amigos comprensivos).\n• Usalo como ancla cuando te sentís perdido.", "highMotivationLowSatisfactionTitle": "Alta Motivación + Baja Satisfacción", "highMotivationLowSatisfactionContent": "¿Te pasó estar re entusiasmado por algo y después sentir un 'meh'? <PERSON><PERSON><PERSON> y <PERSON>, solemos sobreestimar la felicidad futura en un 40-60%.", "highMotivationLowSatisfactionEvolutionaryTitle": "Mirada evolutiva", "highMotivationLowSatisfactionEvolutionaryContent": "Imaginá a un antepasado: cree que una manzana será jugosa y sale a buscarla. Aunque no sea perfecta, encuentra bayas cerca y vale la pena. Ese \"optimismo exagerado\" los empujaba a explorar, aunque la recompensa no fuera ideal. <PERSON><PERSON>, nos pasa lo mismo: nos ilusionamos, pero a veces quedamos con gusto a poco.", "highMotivationLowSatisfactionPracticeTitle": "Práctica con ESTIMAT:", "highMotivationLowSatisfactionPracticeContent": "• Simulá: Antes de lanzarte a algo, imaginá colores, olores, sonidos.\n• Predicción vs. realidad: Anot<PERSON> cuánto creés que vas a disfrutar (de –3 a +3) y después cuánto disfrutaste de verdad.\n• Ajustá: Si esperabas +3 y fue +1, repensá si vale la pena repetir.\n• Reflexión: ¿Dónde podés usar este \"optimismo ancestral\" con un toque más realista?", "lowMotivationHighSatisfactionTitle": "Baja Motivación + Alta Satisfacción", "lowMotivationHighSatisfactionContent": "¿Alguna vez te arrastraste a hacer algo y terminaste sorprendiéndote con lo bien que te sentiste? La Universidad de British Columbia mostró que la gente subestima su disfrute del ejercicio en un 20-30%.", "lowMotivationHighSatisfactionEvolutionaryTitle": "¿Qué dice la evolución?", "lowMotivationHighSatisfactionEvolutionaryContent": "Un antepasado agotado encuentra un arroyo por casualidad y el alivio lo recarga. Esa sorpresa se grababa en su memoria como \"volver a intentarlo\". La dopamina premiaba esas pequeñas victorias. <PERSON><PERSON>, ese \"golpe de alegría\" sigue funcionando.", "lowMotivationHighSatisfactionPracticeTitle": "Práctica con ESTIMAT:", "lowMotivationHighSatisfactionPracticeContent": "• Predicción vs. realidad: <PERSON><PERSON> <PERSON>, anot<PERSON> cuánto creés que vas a disfrutar (–3 a +3), luego comparalo con lo real.\n• Bonus evolutivo: Ese extra de satisfacción es tu cerebro diciendo \"¡Bien hecho!\"\n• Mini-sorpresas: Cambi<PERSON> la rutina (nueva música, otro lugar) para potenciarlo.\n• Reflexión: ¿Qué pequeño reto podés probar esta semana para encontrar una joya inesperada?", "lowMotivationLowSatisfactionTitle": "Baja Motivación + Baja Satisfacción", "lowMotivationLowSatisfactionContent": "¿Y esos días en que no hay ganas ni placer? Puede que esos bajones sean semilleros de tus mejores ideas. Los estudiantes que practicaban autoevaluación reflexiva mostraron un aumento del 20% en su rendimiento.", "lowMotivationLowSatisfactionScienceTitle": "¿Qué dice la ciencia?", "lowMotivationLowSatisfactionScienceContent": "Un estudio de 2020 en Frontiers in Psychology exploró cómo pensar profundamente sobre problemas (lo que llaman \"rumination\" o análisis profundo) puede ayudar cuando estás de bajón. Encontraron que las personas que reflexionaban sobre sus problemas, buscando soluciones, se sentían menos deprimidas después de unas semanas. Esto sugiere que tomarte un momento para analizar tus bajos te ayuda a encontrarle sentido y a recuperar el ánimo más rápido.\n\nOtro experimento, esta vez con estudiantes de medicina, mostró que escribir sobre sus emociones difíciles mejoró su motivación en solo un mes. Los que escribieron sobre sus experiencias negativas se sentían más impulsados a seguir adelante, comparados con los que no lo hicieron. Es como si poner tus pensamientos en papel te diera un mapa para salir del bajón.", "lowMotivationLowSatisfactionWhyWorksTitle": "¿Por qué funciona?", "lowMotivationLowSatisfactionWhyWorksContent": "<PERSON><PERSON><PERSON> escribís o reflexionás sobre un momento bajo, tu cerebro empieza a buscarle sentido. Es como armar un rompecabezas: encontrás pistas sobre qué te llevó ahí y cómo evitarlo en el futuro. Esto no solo te saca del lodo, sino que te hace más fuerte para lo que viene.", "lowMotivationLowSatisfactionEvolutionaryTitle": "Mirada evolutiva:", "lowMotivationLowSatisfactionEvolutionaryContent": "Imaginá a un antepasado que falló en cazar comida. En vez de rendirse, se sentó a pensar: \"¿Qué hice mal? ¿Cómo lo hago mejor?\" Ese hábito de analizar los fracasos era clave para sobrevivir en un mundo duro. <PERSON><PERSON>, tus bajones funcionan igual: son señales de que algo necesita atención. Reflexionar sobre ellos—como hacían nuestros antepasados—te ayuda a aprender, ajustar y hasta descubrir ideas nuevas. Por ejemplo, un estudio sugiere que este tipo de introspección evolucionó para resolver problemas complicados, como conflictos sociales o decisiones importantes, y puede ser una herramienta poderosa para crecer (Andrews & Thomson, 2009).", "lowMotivationLowSatisfactionPracticeTitle": "Práctica con ESTIMAT:", "lowMotivationLowSatisfactionPracticeContent": "• Registrá tu bajón: Escribí sin filtros cómo te sentís y por qué. Por ejemplo: \"Hoy estoy apagado porque no terminé mi proyecto.\"\n• Revisá tu bitácora: Con el tiempo, ESTIMAT te mostrará patrones—like qué te baja el ánimo y qué te ayuda a salir. Estudios sugieren que este hábito de escribir puede aumentar tu bienestar emocional al darte claridad.\n• Hacé algo pequeño: Probá una acción suave, como dar un paseo, escuchar una canción que te gusta o escribir tres cosas por las que estás agradecido. Estas pequeñas chispas pueden encender tu próximo pico.\n• Reflexión: ¿Qué micro-paso podés dar en tu próximo bajón para convertirlo en un trampolín hacia algo mejor?", "generalOverviewTitle": "El Panorama General", "generalOverviewContent": "Registrar tus emociones no es solo un pasatiempo—es una herramienta para conocerte y sacarle jugo a tu cerebro. Mirá los números:\n\n• Sesgo de memoria: Sobrevaloramos lo reciente o intenso, perdiendo momentos valiosos.\n• Impact bias: Sobreestimamos la felicidad futura en un 40-60%.\n• Subestimación: Disfrutamos cosas como el ejercicio 20-30% más de lo que creemos.\n• Recuperación: Reflexionar en los bajos te da 30% más días motivados.\n\nCon un sistema como ESTIMAT, podés:\n• Neutralizar los trucos de tu memoria.\n• Planificar lo que de verdad te llena.\n• Convertir bajones en crecimiento.\n\nÚltima reflexión: ¿Qué pequeño paso podés dar hoy para entender mejor tus emociones? Probá anotar un pico y un valle—los resultados podrían sorprenderte.", "bibliographyTitle": "Bibliografía", "bibliographyContent": "<PERSON>, <PERSON><PERSON>, & <PERSON>, <PERSON> (2009). The bright side of being blue: Depression as an adaptation for analyzing complex problems. Psychological Review, 116(3), 620–654.\n\n<PERSON><PERSON>, <PERSON>, & colleagues (2020). Testing the analytical rumination hypothesis: Exploring the longitudinal effects of problem solving analysis on depression. Frontiers in Psychology, 11, 1344.\n\n<PERSON><PERSON><PERSON>, L<PERSON>, & <PERSON>, J. (2013). Evolutionary psychology: New perspectives on cognition and motivation. Annual Review of Psychology, 64, 201–229.\n\n<PERSON>, <PERSON>, & <PERSON>, T. D. (2007). Prospection: Experiencing the future. Science, 317(5843), 1351–1354.\n\n<PERSON><PERSON>, <PERSON>, & <PERSON>, <PERSON> (1979). Prospect theory: An analysis of decision under risk. Econometrica, 47(2), 263–291.\n\n<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2011). The invisible benefits of exercise. Health Psychology, 30(1), 67–74.\n\n<PERSON><PERSON><PERSON>, B<PERSON>, & colleagues (2018). Sensitivity to \"sunk costs\" in mice, rats, and humans. Science, 361(6398), 178–181.\n\n<PERSON><PERSON><PERSON><PERSON>, <PERSON>, & colleagues (2022). Impact of reflective writing on the emotional quotient of medical students: An observational analytical study. National Journal of Physiology, Pharmacy and Pharmacology, 12(10).", "evolutiveFunctionsHeader": "Funciones Evolutivas", "valueDescriptionGuardian": "Reconocer: Recon<PERSON><PERSON>, consumes, ingieres.\nAlmacenar: Descansas, almacenas y metabolizas.", "valueDescriptionWarrior": "Descartar: <PERSON><PERSON><PERSON>, huy<PERSON>, inhi<PERSON>.\nEjecutar: <PERSON><PERSON>, atacas y te esfuerzas.", "valueDescriptionVersatile": "Observarse: Destacas información negativa, sientes dolor, observas errores.\nTransformarse: Reduces tus errores y adaptas estímulos.", "valueDescriptionFunny": "Motivarse: Destacas información positiva, sientes placer.\nDisfrutarse: Mejora los éxitos y contrasta actitudes e ideas.", "valueDescriptionStrategist": "Analizar: <PERSON><PERSON><PERSON> tendencia<PERSON>, preguntas si algo podría ser falso, analizas.\nPredecir: Predices lo que es más probable y creas hipótesis.", "valueDescriptionTactician": "Simplificar: <PERSON><PERSON><PERSON>, comparas la forma más fácil y rápida.\nRastrear: Buscas, cazas y rastreas.", "valueDescriptionAltruistic": "Empatizar: Empatizas con lo que es importante para los demás.\nDeliberar: Me considero a mí mismo y las necesidades de la mayor cantidad de personas posible, practicando el altruismo eficiente.", "valueDescriptionCollaborator": "Negociar: <PERSON><PERSON><PERSON> a comprender, comunicar.\nColaborar: Cooperas hacia objetivos compartidos.", "latentValuesTitle": "Valores Latentes", "lifePossibilitiesChartTitle": "Gráfico de Posibilidades de Vida", "periodLabel": "Período:", "last7DaysFilter": "Últimos 7 Días", "customRangeFilter": "<PERSON><PERSON>", "customDateRangeFilterTitle": "Filtro de Rango de Fechas Personalizado", "startDateLabel": "<PERSON>cha de Inicio:", "endDateLabel": "<PERSON><PERSON>:", "selectStartDateHint": "Seleccionar fecha de inicio", "selectEndDateHint": "Seleccionar fecha de fin", "resetButton": "Restablecer", "customRangeActiveLabel": "Rango personalizado activo", "showingMomentsLabel": "Mostrando {count} momento{plural}", "whyFocusOnMomentsTitle": "¿Por qué Enfocarse en Momentos?", "whyFocusOnMomentsContent": "Los momentos clave son los bloques de construcción de nuestro proceso de toma de decisiones. Al entender estos momentos, podemos:\n\n• Identificar patrones en nuestro comportamiento\n• Entender qué nos motiva realmente\n• Reconocer qué nos trae satisfacción\n• Tomar decisiones más conscientes\n• Desarrollar mejor autoconciencia", "discoveringLatentValuesTitle": "Descubriendo Valores Latentes", "discoveringLatentValuesContent": "Tus valores latentes emergen de la combinación de:\n\n• **Porcentajes de nivel** (cómo distribuyes entre los cuatro niveles)\n• **Enfoque direccional** (orientación interna vs externa)\n• **Tipo de impacto** (mejora vs empeora)\n\nEstos valores revelan tus fortalezas centrales: {guardianLabel}, {warriorLabel}, {versatileLabel}, {funLabel}, y otros.", "understandingLatentValuesTitle": "Comprendiendo Valores Latentes", "understandingLatentValuesContent": "Tus valores latentes emergen de cómo distribuyes tu enfoque entre los cuatro niveles jerárquicos y si tiendes hacia una orientación interna o externa. Cada combinación revela fortalezas centrales y tendencias naturales que guían tu toma de decisiones y satisfacción en la vida.", "directionsFocusTitle": "Enfoque de Direcciones", "whyHierarchicalOrganizationTitle": "Por qué la Organización Jerárquica de Momentos de Vida Podría Importar", "dataVisualizationBiasReductionTitle": "Visualización de Datos y Reducción de Sesgos", "experimentalEvidenceTitle": "Evidencia Experimental", "evolutionaryPsychologyPerspectiveTitle": "Perspectiva de Psicología Evolutiva", "recognizeYourMomentOrientationLevel": "Reconoce tu momento, tu orientación y nivel", "recognizeYourMoment": "Reconoce tu momento", "organizeTheMomentAndLookForEvidence": "Ahora veamos la distribución de información en el momento y busquemos evidencias.", "orientationTitle": "Orientación", "orientationQuestion": "¿Tu momento estuvo más enfocado en cambiar internamente o externamente?", "threeLevelsTitle": "<PERSON><PERSON><PERSON>", "threeLevelsDescription": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> de tu momento a través de los cuatro niveles de experiencia humana", "pieStepElementalPersonalTitle": "3 Niveles: Elemental vs Personal", "pieStepElementalPersonalDescription": "Para tu momento, ¿enfocaste más en tu estado corporal o en tus intereses/emociones personales?", "pieStepPersonalInformationalTitle": "3 Niveles: Personal vs Informacional", "pieStepPersonalInformationalDescription": "Para tu momento, ¿enfocaste más en tus intereses/emociones personales o en recopilar y procesar información?", "pieStepInformationalSocialTitle": "3 Niveles: Informacional vs Social", "pieStepInformationalSocialDescription": "Para tu momento, ¿enfocaste más en analizar información o en conectar con otros?", "pieStepEvidenceQuestion": "¿Qué evidencia o variables influyeron en tu elección?", "whyMomentsHeaderSubtitle": "<PERSON><PERSON><PERSON> tus momentos, conoce tus patrones", "whyRegisterMomentsObjectivelyTitle": "¿Por qué registrar momentos objetivamente?", "whyRegisterMomentsObjectivelyContent": "Tus picos y valles emocionales no son solo datos sueltos: son como las coordenadas de tu brújula interna. Registrar tu motivación (qué tan impulsado te sentís) y tu satisfacción (qué tan pleno te sentís) te da un mapa claro para planificar metas, rutinas o tareas que realmente sumen a tu vida.", "whyRegisterMomentsObjectivelyHighlight": "📈 Llevar un registro de tus picos de motivación y satisfacción sirve para compensar la distorsión emocional. Te permite tener evidencia de quién sos cuando estás bien, para no perderte de vista cuando estás mal.", "highMotivationHighSatisfactionTitle": "Alta Motivación + Alta Satisfacción", "researchInfoBoxTitle": "🧠 Investigación", "researchInfoBoxContent": "<PERSON><PERSON><PERSON> (1979), nuestro cerebro tiende a darle más peso a lo reciente o lo intenso (regla del pico-final). Por eso, a veces olvidamos momentos valiosos que no fueron tan \"ruidosos\".", "evolutionaryViewInfoBoxTitle": "🔬 Mirada Evolutiva", "evolutionaryViewInfoBoxContent": "Las emociones positivas fuertes señalan oportunidades adaptativas: relaciones exitosas, decisiones alineadas al propósito, logros sociales o personales.", "practiceEstimatInfoBoxTitle": "✍️ Práctica con ESTIMAT", "practiceEstimatInfoBoxContent": "Al revisar varias entradas, ESTIMAT te muestra patrones y te ayuda a replicar esos momentos de alta motivación y satisfacción.", "highMotivationLowSatisfactionIntro": "¿Te pasó estar re entusiasmado por algo y después sentir un \"meh\"? 📱", "impactBiasInfoBoxTitle": "📊 Impact Bias", "impactBiasInfoBoxContent": "<PERSON><PERSON><PERSON> y <PERSON>, solemos sobreestimar la felicidad futura en un", "practiceInfoBoxTitle": "🎯 Práctica", "practiceInfoBoxContent": "• Simulá: <PERSON><PERSON> de lanzarte, imaginá colores, olores, sonidos\n• Predicción vs. realidad: <PERSON><PERSON><PERSON> cuánto creés que vas a disfrutar\n• Ajustá: Si esperabas +3 y fue +1, repensá si vale la pena repetir", "lowMotivationHighSatisfactionIntro": "¿Alguna vez te arrastraste a hacer algo y terminaste sorprendiéndote con lo bien que te sentiste? ⛈️😊", "pleasureUnderestimationInfoBoxTitle": "🏃‍♂️ Subestimación del Placer", "pleasureUnderestimationInfoBoxContent": "La Universidad de British Columbia mostró que la gente subestima su disfrute del ejercicio en un", "effortParadoxInfoBoxTitle": "🧬 Paradoja del Esfuerzo", "effortParadoxInfoBoxContent": "Cada gota de esfuerzo se canjea por un plus extra de satisfacción. Ese \"golpe de alegría\" es tu cerebro diciendo \"¡Bien hecho!\"", "lowMotivationLowSatisfactionIntro": "¿Y esos días en que no hay ganas ni placer? 😔 Puede que esos bajones sean semilleros de tus mejores ideas.", "reflectionPowerInfoBoxTitle": "📈 Poder de la Reflexión", "reflectionPowerInfoBoxContent": "Los estudiantes que practicaban autoevaluación reflexiva mostraron un aumento del", "practiceEstimatLowInfoBoxTitle": "🎯 Práctica con ESTIMAT", "practiceEstimatLowInfoBoxContent": "• Registrá tu bajón: Escribí sin filtros cómo te sentís\n• Revisá tu bitácora: ESTIMAT te mostrará patrones\n• Hacé algo pequeño: Un paseo, una canción, tres agradecimientos", "generalOverviewIntro": "Registrar tus emociones no es solo un pasatiempo—es una herramienta para conocerte y sacarle jugo a tu cerebro.", "memoryBiasStatTitle": "🧠 Sesgo de memoria", "memoryBiasStatContent": "Sobrevaloramos lo reciente o intenso", "impactBiasStatTitle": "🎯 Impact bias", "impactBiasStatContent": "Sobreestimamos la felicidad futura", "underestimationStatTitle": "💪 Subestimación", "underestimationStatContent": "Disfrutamos el ejercicio más de lo que creemos", "recoveryStatTitle": "🔄 Recuperación", "recoveryStatContent": "Reflexionar te da más días motivados", "generalOverviewConclusion": "¿Qué pequeño paso podés dar hoy para entender mejor tus emociones? Probá anotar un pico y un valle—los resultados podrían sorprenderte. ✨", "whyHierarchicalHeaderSubtitle": "Por qué la Organización Jerárquica de Momentos de Vida Podría Importar", "whyHierarchicalImportantNote": "Importante: Si ya estás experimentando bienestar constante y satisfacción profunda, la organización adicional probablemente sea innecesaria. Este enfoque parece más relevante durante transiciones, decisiones complejas o insatisfacción persistente.", "informationTheoryPerspectiveTitle": "Perspectiva de Teoría de la Información", "debunkingCommonMythsTitle": "<PERSON><PERSON><PERSON><PERSON>", "selfPerceptionBiasesTitle": "El Problema de los Sesgos de Autopercepción", "visualProportionsTitle": "Ventajas de las Proporciones Visuales", "statsVsIntuitionTitle": "Estadísticas Personales vs. Intuición", "memoryHierarchyTitle": "Experimentos de Jerarquía de Memoria", "decisionFatigueTitle": "Estudios de Fatiga de Decisión", "millerNumberTitle": "El Número Mágico de Miller", "socialLevelTitle": "4. Social", "informationalLevelTitle": "3. Informacional", "personalLevelTitle": "2. Personal", "elementalLevelTitle": "1. Elemental", "collaborateFunction": "Colaborar", "negotiateFunction": "Negociar", "collaboratorValue": "COLABORADOR", "diplomatSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deliberateFunction": "Deliberar", "empathizeFunction": "<PERSON><PERSON><PERSON><PERSON>", "altruisticValue": "ALTRUISTA", "empatheticSubtitle": "Empá<PERSON>o", "predictFunction": "Predecir", "analyzeFunction": "<PERSON><PERSON><PERSON>", "strategistValue": "ESTRATEGA", "analystSubtitle": "<PERSON><PERSON><PERSON>", "trackFunction": "<PERSON><PERSON><PERSON><PERSON>", "simplifyFunction": "Simplificar", "tacticianValue": "TÁCTICO", "synthesizerSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selfEnjoyFunction": "Auto-Disfrutar", "selfMotivateFunction": "Auto-Motivar", "funnyValue": "DIVERTIDO", "enthusiasticSubtitle": "<PERSON><PERSON><PERSON><PERSON>", "selfTransformFunction": "Auto-Transformar", "selfObserveFunction": "Auto-Observar", "versatileValue": "VERSÁTIL", "selfSeerSubtitle": "Auto-Observador", "executeFunction": "<PERSON><PERSON><PERSON><PERSON>", "discardFunction": "Descar<PERSON>", "warriorValue": "GUERRERO", "releaserSubtitle": "Liberador", "storeFunction": "Almacenar", "recognizeFunction": "Reconocer", "guardianValue": "GUARDIÁN", "nurturerSubtitle": "Cuidador", "availabilityBiasContent": "Sesgo de Disponibilidad (T<PERSON> & Kahneman, 1973): Es probable que recordemos desproporcionalmente eventos recientes o emocionales, lo que puede distorsionar nuestra percepción de los patrones de vida.", "overestimationLabel": "Sobreestimación", "overestimationSublabel": "de patrones recientes vs. reales", "decisionDistortionLabel": "Distorsión de Decisión", "decisionDistortionSublabel": "de decisiones basadas en memoria", "hierarchicalVisualizationNote": "La visualización jerárquica de momentos puede neutralizar este sesgo proporcionando una representación más objetiva de los patrones temporales.", "clevelandMcGillContent": "Cleveland & McGill (1984): La percepción visual de proporciones parece ser significativamente más precisa que las memorias narrativas para evaluar distribuciones temporales.", "potentialPersonalApplicationsTitle": "Aplicaciones Personales Potenciales:", "personalApplicationsList": "• Distribución real de tiempo vs. percepción\n• Patrones de energía y estados emocionales\n• Frecuencia de diferentes tipos de experiencia\n• Progreso hacia objetivos de largo plazo", "visualizationDiscrepanciesNote": "Estas visualizaciones pueden revelar discrepancias entre percepción subjetiva y realidad objetiva, facilitando decisiones más informadas.", "personalIntuitionParadoxContent": "Paradoja de la Intuición Personal: Aunque confiamos en nuestra intuición para decisiones personales, es probable que apliquemos análisis estadístico riguroso para decisiones profesionales o financieras.", "financialDecisionsLabel": "Decisiones Financieras", "financialDecisionsSublabel": "usan datos objetivos", "personalDecisionsLabel": "Decisiones Personales", "personalDecisionsSublabel": "usan datos objetivos", "potentialImprovementLabel": "Mejora Potencial", "potentialImprovementSublabel": "con organización sistemática", "hierarchicalAnalyticalNote": "La organización jerárquica puede permitir aplicar rigor analítico a las decisiones de vida manteniendo flexibilidad emocional e intuitiva.", "memoryHierarchyContent": "<PERSON><PERSON> et al. (1969): La organización jerárquica puede mejorar la recordación en aproximadamente 200% comparado con la presentación aleatoria.", "baselineLabel": "Línea Base", "randomPresentationSublabel": "capacidad de recordación", "hierarchicalOrganizationLabel": "Organización Jerárquica", "hierarchicalImprovementSublabel": "mejora en la recordación", "brainProcessesHierarchically": "Implicación probable: Tu cerebro probablemente procesa información jerárquicamente por naturaleza. Luchar contra esta estructura posiblemente desperdicia recursos cognitivos.", "decisionFatigueContent": "<PERSON><PERSON><PERSON> et al. (1998): Después de decisiones no estructuradas repetidas, la calidad de las decisiones probablemente declina significativamente.", "evolutionaryPerspectiveTitle": "Perspectiva Evolutiva:", "ancestorsDecisionsContent": "Nuestros ancestros probablemente enfrentaban decisiones diarias limitadas en jerarquías sociales estructuradas. El caos moderno de opciones desorganizadas puede exceder nuestra capacidad cognitiva.", "preOrganizedStructures": "Estructuras jerárquicas pre-organizadas podrían mantener la calidad de las decisiones incluso bajo carga cognitiva.", "millerNumberContent": "<PERSON> (1956): Los humanos pueden posiblemente mantener 7±2 elementos no relacionados en la memoria de trabajo, pero probablemente pueden procesar 7±2 categorías, cada una conteniendo 7±2 subcategorías.", "individualItemsLabel": "Elementos Individuales", "workingMemoryLimitSublabel": "límite de la memoria de trabajo", "hierarchicalCapacityLabel": "Capacidad Jerárquica", "organizedElementsSublabel": "elementos organizados", "exponentialProcessingCapacity": "Esto podría crear capacidad de procesamiento exponencial a través de la jerarquía, liberando recursos mentales para reconocimiento de patrones y planificación futura.", "ancestralMismatchContent": "Los humanos modernos posiblemente enfrentan aproximadamente 35,000 decisiones diarias, mientras que nuestros ancestros probablemente encontraban 70-100 decisiones estructuradas en jerarquías sociales predecibles.", "ancestralDecisionsLabel": "Decisiones Ancestrales", "structuredPerDaySublabel": "estructuradas/día", "modernDecisionsLabel": "Decisiones Modernas", "unstructuredPerDaySublabel": "no estructuradas/día", "schwartzOptionsContent": "<PERSON> (2004): Más de 8-10 opciones no estructuradas pueden disminuir la satisfacción en 25% y la calidad de las decisiones en 15%.", "foragingEfficiencyContent": "<PERSON> & <PERSON> (1986): Los animales que organizaron el comportamiento de búsqueda jerárquicamente (territorio → parches → recursos específicos) probablemente mostraron 40-60% mejor eficiencia energética.", "energyEfficiencyLabel": "Eficiencia Energética", "hierarchicalOrganizationSublabel": "organización jerárquica", "goalAchievementLabel": "Logro de Objetivos", "structuredFrameworksSublabel": "marcos estructurados", "gigerenzerFrameworksContent": "<PERSON><PERSON><PERSON><PERSON> (2007): Las personas que usan marcos de decisión jerárquicos posiblemente logran objetivos 35% más rápido con 50% menos esfuerzo.", "compressionAdvantageContent": "Shannon (1948): La organización jerárquica probablemente logra compresión de datos óptima. Aplicado a experiencias de vida, esto podría permitir procesar exponencialmente más información.", "applicationToMomentsTitle": "Aplicación a Momentos:", "compressionMomentsContent": "En lugar de recordar cientos de experiencias desconectadas, la organización jerárquica posiblemente permite comprimir momentos similares en categorías, liberando recursos mentales para reconocimiento de patrones y planificación futura.", "predictionMachineContent": "<PERSON> (2013): El cerebro posiblemente opera como una \"máquina de predicción\", generando constantemente modelos de experiencias futuras basados en patrones pasados.", "neuralReductionLabel": "<PERSON>ucción Neural", "predictableExperiencesSublabel": "experiencias predecibles", "unpredictedActivityLabel": "Actividad Impredecible", "neuralActivitySublabel": "actividad neural", "organizedMomentTracking": "El seguimiento organizado de momentos probablemente crea mejores modelos predictivos, reduciendo la carga cognitiva y mejorando la precisión de la toma de decisiones futuras.", "entropyReductionContent": "<PERSON><PERSON><PERSON> et al. (2001): Las redes neuronales que usan procesamiento jerárquico posiblemente logran eficiencia superior en la transmisión de información comparado con estructuras planas.", "lifeApplicationEntropy": "Aplicación a la vida: La organización jerárquica de momentos probablemente permite extraer máximo conocimiento de las experiencias mientras minimiza el ruido cognitivo.", "creativityMythCounterTitle": "Contra-evidencia:", "creativityMythCounterContent": "• Stokes (2005): Los profesionales creativos con marcos organizacionales posiblemente producen trabajo más innovador\n• Schwartz (2004): Demasiadas opciones no estructuradas probablemente disminuyen la producción creativa\n• La organización jerárquica probablemente reduce el ruido cognitivo, liberando recursos mentales para el pensamiento creativo", "successMythCounterContent": "• <PERSON><PERSON> (2016): Los ejecutantes de élite en todos los dominios probablemente usan sistemas de práctica y reflexión altamente estructurados\n• Los individuos de alto rendimiento probablemente muestran habilidades organizacionales superiores, no menos estructura\n• Las sociedades cazadoras-recolectoras exitosas probablemente tenían sistemas de organización jerárquica complejos", "hierarchyMythCounterContent": "• Todas las sociedades de primates exitosas probablemente exhiben organización jerárquica con roles claros\n• El cerebro humano probablemente evolucionó el procesamiento jerárquico como su arquitectura fundamental\n• Incluso las sociedades igualitarias posiblemente mantienen organización jerárquica para diferentes dominios", "simplicityMythCounterContent": "• La complejidad apropiada probablemente coincide con las demandas ambientales\n• La sobresimplificación posiblemente lleva al fallo del sistema\n• La complejidad bien estructurada probablemente reduce la carga cognitiva\n• La organización jerárquica probablemente logra un equilibrio óptimo entre simplicidad y riqueza de información", "anxietyMythCounterContent": "• La incertidumbre no estructurada probablemente genera más ansiedad que la complejidad organizada\n• Los marcos claros posiblemente reducen la ansiedad de decisión\n• La organización jerárquica probablemente proporciona estructura predictiva que calma el sistema nervioso\n• Los estudios sugieren que la claridad organizacional reduce las hormonas del estrés", "fullPresentationMethodologyTitle": "La Metodología", "fullPresentationMethodologyContent": "Nuestro enfoque combina insights psicológicos con análisis práctico:\n\n1. **Identificación de Momentos**: Reconoce momentos clave en tu vida\n2. **Análisis Dimensional**: Categoriza a través de cuatro niveles\n3. **Enfoque Direccional**: Comprende orientación interna vs externa\n4. **Evaluación de Impacto**: Evalúa efectos de motivación y satisfacción\n5. **Reconocimiento de Patrones**: Descubre tus valores latentes", "fullPresentationFourLevelsTitle": "Cuatro Niveles de Experiencia Humana", "fullPresentationFourLevelsContent": "Estos niveles funcionan jerárquicamente, construyendo unos sobre otros para crear tu experiencia completa.", "fullPresentationDirectionalFocusTitle": "Enfoque Direccional", "fullPresentationDirectionalFocusContent": "Todo momento tiene componentes internos y externos, pero una dirección típicamente domina. Entender esto ayuda a revelar tus tendencias y preferencias naturales.", "fullPresentationPracticalApplicationTitle": "Aplicación Práctica", "fullPresentationPracticalApplicationContent": "Usa esta metodología para:\n\n• **Rastrear patrones** en tu toma de decisiones\n• **Identificar disparadores** para motivación y satisfacción\n• **Entender conflictos** entre diferentes aspectos de ti mismo\n• **Tomar mejores decisiones** alineadas con tus valores\n• **Desarrollar estrategias** para crecimiento personal", "fullPresentationConclusionTitle": "Tu Viaje <PERSON>", "fullPresentationConclusionContent": "Ahora que entiendes la metodología, puedes:\n\n• Come<PERSON><PERSON> a registrar tus momentos clave\n• <PERSON><PERSON><PERSON> tus patrones a lo largo del tiempo\n• Descubrir tus valores latentes únicos\n• Usar insights para desarrollo personal\n\nRecuerda: La autoconciencia es el primer paso hacia la vida intencional.", "fullPresentationElementalDescription": "Respuestas físicas e instintivas", "fullPresentationPersonalDescription": "Experiencias y emociones individuales", "fullPresentationInformationalDescription": "Procesos intelectuales y analíticos", "fullPresentationSocialDescription": "Conexiones interpersonales y culturales", "ancestralMismatchTitle": "Desajuste del Entorno Ancestral", "socialHierarchyTitle": "Hipótesis de Jerarquía Social", "foragingEfficiencyTitle": "Modelo de Eficiencia de Búsqueda", "compressionAdvantageTitle": "La Ventaja de la Compresión", "predictionMachineTitle": "El Cerebro como Máquina de Predicción", "entropyReductionTitle": "Principio de Reducción de Entropía", "creativityMythTitle": "Mito: 'La organización mata la creatividad y la espontaneidad'", "successMythTitle": "<PERSON><PERSON>: 'Las personas exitosas no necesitan sistemas—simplemente improvisan'", "hierarchyMythTitle": "Mito: 'La jerarquía es antinatural y opresiva'", "simplicityMythTitle": "<PERSON>to: 'Lo simple siempre es mejor que lo complejo'", "anxietyMythTitle": "Mito: 'La organización es solo para personas ansiosas o controladoras'", "socialHierarchyContent": "<PERSON><PERSON><PERSON> (2017): Los humanos y otros primates probablemente muestran los niveles más bajos de hormonas del estrés cuando su posición social está claramente definida, es predecible y está controlada internamente.", "stressReductionElementsTitle": "Elementos Clave para la Reducción del Estrés:", "stressReductionElementsList": "• Claridad jerárquica: Posición definida\n• Predictibilidad: Reglas consistentes\n• Control interno: Agencia dentro de la estructura", "randomPresentationLabel": "Presentación Aleatoria", "hierarchicalOrganizationBenefits": "La organización jerárquica de la vida puede imitar estructuras sociales ancestrales exitosas, probablemente reduciendo el estrés y mejorando la toma de decisiones.", "fourLevelsProcessTitle": "Proceso de Cuatro Niveles", "hierarchicalStructureTitle": "Estructura Jerárquica", "hierarchicalStructureDescription": "Las cuatro dimensiones siguen una estructura jerárquica:", "levelsEquationText": "Elemental + Personal + Informacional + Social = 100%", "elementalAllocationText": "<PERSON>uando asignas un porcentaje al Elemental, el porcentaje restante se distribuye entre Personal, Informacional y Social.", "personalAllocationText": "<PERSON>uando asignas un porcentaje al Personal, el porcentaje restante se distribuye entre Informacional y Social.", "informationalAllocationText": "<PERSON>uando asignas un porcentaje al Informacional, el porcentaje restante va al Social.", "directionsExplanationText": "Interior + Exterior = 100%\n\nEstas dimensiones representan tu orientación o enfoque hacia cada momento.", "descriptionNotAvailable": "Descripción no disponible.", "pleaseEnterTitle": "Por favor, ingresa un título", "titleMustBeTwoWords": "El título debe tener exactamente dos palabras", "untitledMoment": "Momento Sin Título", "whyMomentsTitle": "<PERSON><PERSON> <PERSON>", "whyMomentsSubtitle": "<PERSON><PERSON><PERSON> tus momentos, conoce tus patrones", "whyHierarchicalTitle": "Por Qué Organización Jerárquica", "whyHierarchicalSubtitle": "Por Qué la Organización Jerárquica de los Momentos de Vida Puede Importar", "highMotivationHighSatisfactionContent": "<PERSON><PERSON><PERSON> (1979), nuestro cerebro tiende a darle más peso a lo reciente o lo intenso (regla del pico-final). Por eso, a veces olvidamos momentos valiosos que no fueron tan 'ruidosos'.", "importantNote": "Importante: Si ya estás experimentando bienestar consistente y satisfacción profunda, la organización adicional probablemente es innecesaria. Este enfoque parece más relevante durante transiciones, decisiones complejas o insatisfacción persistente.", "dataVisualizationTitle": "Visualización de Datos y Reducción de Sesgo", "dataVisualizationContent": "La visualización jerárquica de momentos podría contrarrestar el sesgo de disponibilidad proporcionando una representación más objetiva de los patrones temporales. La percepción visual de proporciones parece ser significativamente más precisa que las memorias narrativas.", "experimentalEvidenceContent": "La organización jerárquica podría mejorar el recuerdo en aproximadamente 200% comparado con la presentación aleatoria. Después de decisiones repetidas no estructuradas, la calidad de la decisión probablemente declina significativamente.", "evolutionaryPsychologyTitle": "Perspectiva de Psicología Evolutiva", "evolutionaryPsychologyContent": "Los humanos modernos posiblemente enfrentan aproximadamente 35,000 decisiones diarias, mientras que nuestros ancestros probablemente encontraban 70-100 decisiones estructuradas en jerarquías sociales predecibles.", "informationTheoryTitle": "Perspectiva de Teoría de la Información", "informationTheoryContent": "La organización jerárquica probablemente logra compresión de datos óptima. Aplicada a las experiencias de vida, esto podría permitir procesar exponencialmente más información.", "scientificFooter": "Basado en investigación científica • Diseñado para tu crecimiento personal", "guardianValueDescription": "Reconocer: Reconoc<PERSON>, consumes, ingieres.\\nAlmacenar: Descansas, almacenas y metabolizas.", "warriorValueDescription": "Descartar: <PERSON><PERSON><PERSON>, huy<PERSON>, inhibes.\\nEjecutar: <PERSON><PERSON>, atacas y te esfuerzas.", "versatileValueDescription": "Auto-Observar: Destacas información negativa, sientes dolor, observas errores.\\nAuto-Transformar: Reduces tus errores y adaptas estímulos.", "funValueDescription": "Auto-Motivar: Destacas información positiva, sientes placer.\\nAuto-Disfrutar: Realzas éxitos y contrastas actitudes e ideas.", "strategistValueDescription": "Analizar: <PERSON><PERSON><PERSON> tendencia<PERSON>, preguntas si algo podría ser falso, analizas.\\nPredecir: Predices lo que es más probable y creas hipótesis.", "tacticalValueDescription": "Simplificar: <PERSON><PERSON><PERSON>, comparas la forma más fácil y rápida.\\nRastrear: Buscas, cazas y rastreas.", "altruistValueDescription": "Empatizar: Empatizas con lo que es importante para otros.\\nDeliberar: Consideras a ti mismo y las necesidades de tantas personas como sea posible, practicando altruismo eficiente.", "collaboratorValueDescription": "Negociar: <PERSON><PERSON><PERSON> a entender, comunicar.\\nColaborar: Cooperas hacia objetivos compartidos.", "functionCollaborate": "Colaborar", "functionNegotiate": "Negociar", "functionDeliberate": "Deliberar", "functionEmpathize": "<PERSON><PERSON><PERSON><PERSON>", "functionPredict": "Predecir", "functionAnalyze": "<PERSON><PERSON><PERSON>", "functionTrack": "<PERSON><PERSON><PERSON><PERSON>", "functionSimplify": "Simplificar", "functionSelfEnjoy": "Auto-Disfrutar", "functionSelfMotivate": "Auto-Motivar", "functionSelfTransform": "Auto-Transformar", "functionSelfObserve": "Auto-Observar", "functionExecute": "<PERSON><PERSON><PERSON><PERSON>", "functionDiscard": "Descar<PERSON>", "functionStore": "Almacenar", "functionRecognize": "Reconocer", "valueCollaborator": "Colaborador", "valueAltruistic": "Altruista", "valueStrategist": "Estratega", "valueTactician": "Táctico", "valueFunny": "Divertido", "valueVersatile": "<PERSON><PERSON><PERSON><PERSON>", "valueWarrior": "Guerrero", "valueGuardian": "Guardián", "subtitleDiplomat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitleEmpathetic": "Empá<PERSON>o", "subtitleAnalyst": "<PERSON><PERSON><PERSON>", "subtitleSynthesizer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitleEnthusiastic": "<PERSON><PERSON><PERSON><PERSON>", "subtitleSelfSeer": "Auto-Observador", "subtitleReleaser": "Liberador", "subtitleNurturer": "Cuidador", "descriptionGuardian": "Valor enfocado en preservar, proteger y mantener estabilidad. Representa la capacidad de reconocer y almacenar información importante para seguridad y continuidad.", "descriptionWarrior": "Valor enfocado en acción, ejecución y superación de obstáculos. Representa la capacidad de ejecutar decisiones y descartar lo que ya no sirve.", "descriptionVersatile": "Valor enfocado en adaptabilidad y auto-observación. Representa la capacidad de transformarse y observarse a sí mismo para crecimiento personal.", "descriptionFunny": "Valor enfocado en placer, motivación y energía positiva. Representa la capacidad de auto-disfrutar y auto-motivar para mantener el bienestar.", "descriptionStrategist": "Valor enfocado en planificación y análisis profundo. Representa la capacidad de predecir escenarios y analizar información compleja.", "descriptionTactician": "Valor enfocado en síntesis y seguimiento eficiente. Representa la capacidad de simplificar complejidades y rastrear progreso.", "descriptionAltruistic": "Valor enfocado en empatía y consideración por otros. Representa la capacidad de deliberar y empatizar para el bien común.", "descriptionCollaborator": "Valor enfocado en cooperación y negociación. Representa la capacidad de colaborar efectivamente y negociar soluciones mutuamente beneficiosas.", "guardianDisplayName": "GUARDIÁN", "guardianDisplayNameSmall": "Cuidador", "warriorDisplayName": "GUERRERO", "warriorDisplayNameSmall": "Liberador", "versatileDisplayName": "VERSÁTIL", "versatileDisplayNameSmall": "AutoObservador", "funDisplayName": "DIVERTIDO", "funDisplayNameSmall": "<PERSON><PERSON><PERSON><PERSON>", "strategistDisplayName": "ESTRATEGA", "strategistDisplayNameSmall": "<PERSON><PERSON><PERSON>", "tacticalDisplayName": "TÁCTICO", "tacticalDisplayNameSmall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "altruistDisplayName": "ALTRUISTA", "altruistDisplayNameSmall": "Empá<PERSON>o", "collaboratorDisplayName": "COLABORADOR", "collaboratorDisplayNameSmall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardianDescription": "Reconocer: Reconoc<PERSON>, consumes, ingieres.\\nAlmacenar: Descansas, almacenas y metabolizas.", "warriorDescription": "Descartar: <PERSON><PERSON><PERSON>, huy<PERSON>, inhibes.\\nEjecutar: <PERSON><PERSON>, atacas y te esfuerzas.", "versatileDescription": "Auto-Observar: Destacas información negativa, sientes dolor, observas errores.\\nAuto-Transformar: Reduces tus errores y adaptas estímulos.", "funDescription": "Auto-Motivar: Destacas información positiva, sientes placer.\\nAuto-Disfrutar: Realzo éxitos y contrasto actitudes e ideas.", "strategistDescription": "Analizar: <PERSON><PERSON><PERSON> tendencia<PERSON>, preguntas si algo podría ser falso, analizas.\\nPredecir: Predices lo que es más probable y creas hipótesis.", "tacticalDescription": "Simplificar: <PERSON><PERSON><PERSON>, comparas la forma más fácil y rápida.\\nRastrear: Buscas, cazas y rastreas.", "altruistDescription": "Empatizar: Empatizas con lo que es importante para otros.\\nDeliberar: Consideras a ti mismo y las necesidades de tantas personas como sea posible, practicando altruismo eficiente.", "collaboratorDescription": "Negociar: <PERSON><PERSON><PERSON> a entender, comunicar.\\nColaborar: Cooperas hacia objetivos compartidos.", "exportDataTitle": "Exportar Datos", "exportOptionsTitle": "Opciones de Exportación", "exportToCSVTitle": "Exportar a CSV", "exportToCSVSubtitle": "Guarda los datos de tus momentos como un archivo CSV", "exportSuccessfulTitle": "¡Exportación Exitosa!", "fileSavedToLabel": "Archivo guardado en: {filePath}", "viewFileButton": "Ver Archivo", "csvDataExportedSuccessfully": "Datos CSV exportados exitosamente:", "exportFailedTitle": "Falló la Exportación", "noMomentsToExport": "No hay momentos para exportar.", "errorExportingData": "Error al exportar datos: {error}", "fileNotFound": "Archivo no encontrado: {filePath}", "errorOpeningFile": "Error al abrir archivo: {error}"}